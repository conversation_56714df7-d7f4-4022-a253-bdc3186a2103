{"device": "<PERSON><PERSON>", "status": "Status", "created": "Created", "response": "Response", "no-more-results": "No more results", "get-started": "Get Started", "login": "Log in using an existing account", "email-login-error": "Something went wrong while sending the email. Please try again later.", "email-login-success": "A login link has been sent to your email address. The link will expire in 60 minutes.", "dont-have-an-account": "Don't have an account?", "create-a-free-account": "Create a free account", "sign-up": "Sign up", "agree-message-part": "By clicking {{button}}, you agree to our ", "terms": "Terms and Conditions", "privacy": "Privacy Statement", "and": "and", "sign-in": "Sign in", "sign-in-with-email": "Sign in with <PERSON><PERSON>", "sign-in-with-password": "Sign in with Password", "create-a-new-account": "Create a new account", "create-account": "Create Account", "change-password": "Change Password", "already-have-an-account": "Already have an account?", "continue-with-saml-sso": "Continue with SSO", "send-magic-link": "Send Magic Link", "your-name": "Your Name", "your-email": "Your Email", "save-changes": "Save Changes", "successfully-updated": "Changes saved successfully.", "all-teams": "All Teams", "team-invite": "is inviting you to join their team.", "successfully-joined": "You have successfully created your account.", "invitation-sent": "Invitation sent!", "invitation-deleted": "Invitation deleted", "send-invite": "Invite", "invite-new-member": "Invite New Member", "accept-invitation": "Join the Team", "create-team": "Create Team", "remove-team": "Remove Team", "remove-team-warning": "Deleting the team will delete all resources and data associated with the team forever. This action cannot be undone.", "team-created": "Team created successfully.", "leave-team": "Leave Team", "leave-team-success": "You have left the team successfully.", "choose-team": "Choose your team", "members-of-a-team": "Members of a team have access to specific areas, such as a new release or a new application feature.", "accept-invite": "You can accept the invitation to join the team by clicking the button below.", "invite-create-account": "To continue, you must either create a new account or login to an existing account.", "no-active-team": "You do not have any active team.", "name": "Name", "email": "Email", "members": "Members", "delete-member-warning": "Removing {{name}} ({{email}}) will revoke their access to the team. This action cannot be undone.", "confirm-delete-member": "Confirm deletion of member", "delete-member-invitation-warning": "Removing this invitation will prevent {{email}} from joining the team. This action cannot be undone.", "confirm-delete-member-invitation": "Confirm deletion of member invitation", "delete-webhook-warning": "Deleting this webhook will permanently terminate its functionality. This action cannot be undone.", "confirm-delete-webhook": "Confirm delete webhook", "role": "Role", "url": "URL", "actions": "Actions", "created-at": "Created At", "remove": "Remove", "edit": "Edit", "close": "Close", "unknown-error": "Unknown Error", "add-member": "Invite Member", "add-webhook": "Add Webhook", "webhook-created": "Webhook created successfully.", "webhook-deleted": "Webhook deleted successfully.", "create-webhook": "Create Webhook", "webhook-create-desc": "Create webhook to listen to events from app.", "edit-webhook-endpoint": "Edit Webhook Endpoint", "events-to-send": "Events to send", "events-description": "You can choose which events are sent to which endpoint. By default, all messages are sent to all endpoints.", "kickstart-your-enterprise": "Kickstart your enterprise app development with Next.js SaaS Starter Kit", "enterprise-saas-kit": "Enterprise SaaS Starter Kit", "frequently-asked": "Frequently asked questions", "pricing": "Pricing", "buy-now": "Buy now", "features": "Features", "password-reset-link-sent": "Password reset link sent", "email-password-reset-link": "Email Password Reset Link", "reset-password": "Reset Password", "forgot-password": "Forgot your password?", "password-updated": "Password updated successfully.", "teams": "Teams", "account": "Account", "password": "Password", "settings": "Settings", "audit-logs": "<PERSON><PERSON>", "webhooks": "Webhooks", "team": "Team", "team-name": "Team Name", "current-password": "Current Password", "new-password": "New Password", "continue-with-github": "Continue with GitHub", "continue-with-google": "Continue with Google", "email-placeholder": "<EMAIL>", "confirm-password": "Confirm Password", "enter-new-password": "Enter new password", "member-role-updated": "Member role updated successfully.", "member-deleted": "Member deleted successfully.", "team-slug": "Team Slug", "team-domain": "Domain", "new-api-key": "New API Key", "api-key-created": "API key created successfully", "api-key-deleted": "API key deleted successfully", "api-key-description": "API keys allow your app to communicate with our APIs.", "create-api-key": "Create API Key", "new-api-warning": "Save this API key somewhere safe. You won't be able to see it again once you close this dialog.", "revoke": "Revoke", "cancel": "Cancel", "revoke-api-key": "Revoke API Key", "active": "Active", "no-api-key-title": "You haven't created any API Keys yet", "revoke-api-key-confirm": "Are you sure you want to revoke this API Key? This action can not be undone.", "delete": "Delete", "copy-to-clipboard": "Copy", "copied-to-clipboard": "Copied to clipboard", "api-key": "API Key", "remove-team-confirmation": "Are you sure you want to delete the team? Deleting the team will delete all resources and data associated with the team forever.", "team-removed-successfully": "Team removed successfully.", "leave-team-confirmation": "Are you sure you want to leave the team? You'll lose access to all resources and data associated with the team.", "all-products": "All Products", "new-team": "New Team", "profile": "Profile", "team-not-found": "Team not found", "no-webhook-title": "You haven't created any webhook yet", "sp-saml-config-title": "Service Provider (SP) SAML Configuration", "sp-saml-config-description": "Your Identity Provider (IdP) will ask for the following details while configuring the SAML application. Share this information with your IT administrator.", "sp-acs-url": "ACS (Assertion Consumer Service) URL / Single Sign-On URL / Destination URL", "sp-entity-id": "SP Entity ID / Identifier / Audience URI / Audience Restriction", "assertion-signature": "Assertion Signature", "signature-algorithm": "Signature Algorithm", "assertion-encryption": "Assertion Encryption", "email-verified": "Your email has been successfully verified. Thank you for confirming your account.", "confirm-email": "Confirm your email address", "confirm-email-description": "Please click the verification link sent to your email to activate your account. There is a chance it might have ended up in your Spam folder, please check there if you do not receive the email.", "allow-only-work-email": "Please use your work email to create an account.", "webhook-updated": "Webhook updated successfully.", "system": "System", "dark": "Dark", "light": "Light", "theme": "Theme", "verify-your-account": "Verify your account", "verify-account-expired": "This verification link has expired. Please enter the email address associated with your account, and we'll send you another verification link.", "verify-account-link-sent": "Verification link sent. Please check your email.", "homepage-title": "Enterprise SaaS Starter Kit by BoxyHQ", "sign-up-title": "Sign Up | BoxyHQ ", "login-title": "Login | BoxyHQ", "forgot-password-title": "Reset Password | BoxyHQ", "magic-link-title": "Login With Magic Link | BoxyHQ", "resend-token-title": "Resend Email Token | BoxyHQ", "invitation-title": "You've been invited to join ", "email-address": "Email Address", "email-address-description": "The email address you use to sign in to your account.", "name-appearance": "This is how your name will appear in the interface.", "change-password-text": "You can change your password here.", "change-theme": "This will change the theme of the app.", "avatar": "Avatar", "custom-avatar": "Click on the avatar to upload a custom one from your files.", "avatar-type": "Accepted file types: .png, .jpg. Max file size: 2MB.", "confirm-your-email": "Please confirm your email address before you can continue.", "pending-invitations": "Pending Invitations", "description-invitations": "Invitations that have been sent to users but have not yet been accepted.", "require-email": "Email is a required field", "required-role": "Role is required", "team-listed": "Your teams are listed here.", "team-settings": "Team Settings", "team-settings-config": "Team settings and configuration.", "resend-link": "Resend Link", "expires-at": "Expires At", "exceeded-login-attempts": "Your account has been locked after too many failed login attempts. We sent you an email with a link to unlock it.", "account-unlocked": "Your account has been unlocked. Please try logging in again. Contact support if you need further assistance.", "security": "Security", "browser-sessions": "Browser Sessions", "manage-sessions": "Manage and logout your active sessions on other browsers and devices.", "this-browser": "This browser", "other": "Other", "remove-browser-session": "Remove Browser Session", "remove-other-browser-session-warning": "Once removed you will be logged out of the app on that browser.", "remove-current-browser-session-warning": "You are about to remove your current session. Once you do this, you will be logged out of the app.", "session-removed": "Browser session removed.", "remove-team-restricted": "Please contact our support if you want to remove this team. We will help you with that.", "subscriptions": "Subscriptions", "stripe-checkout-fallback-error": "Something went wrong while initiating checkout, please try again later.", "create-link": "Create Link", "select-a-session-to-delete": "Please select a session to delete", "api-keys": "API Keys", "api-keys-description": "API keys allow you to authenticate with the API.", "product-placeholder": "This is just a placeholder for the products page.", "request-new-link": "Request new link", "unlock-account-link-sent": "A new link has been sent to your email address.", "or": "or", "webhooks-description": "Webhooks are used to send notifications to your external apps.", "members-description": "Team members and their roles.", "invitation-link-created": "Invitation link created. Share it with your team member.", "invitation-link-deleted": "Invitation link deleted.", "share-invitation-link": "Share your team invite link", "delete-invitation-link": "Delete invitation link", "delete-invitation-warning": "Are you sure you want to delete this invitation link? Any future attempts to join your team using this link will fail.", "invite-via-link": "Invite via link", "invite-via-email": "Invite via email", "delete-link": "Delete link", "accept-invitation-email-domain-instruction": "To accept this invitation, you will need to sign out and then sign in or create a new account using the allowed email domains.", "email-domain-not-allowed": "Your email address domain {{emailDomain}} is not allowed for this invitation. Please use an email with {{allowedDomainsString}}.", "accept-invitation-email-instruction": "To accept this invitation, you will need to sign out and then sign in or create a new account using the same email address used in the invitation.", "email-mismatch-error": "Your email address {{email}} does not match the email address this invitation was sent to.", "x": "x", "close-sidebar": "Close sidebar", "open-sidebar": "Open sidebar", "switch-theme": "Change Theme", "logout": "Sign out", "guides": "guides", "download": "Download", "plan": "Plan", "start-date": "Start Date", "end-date": "End Date", "current": "Current", "manage-subscription": "Manage your subscription", "manage-billing-information": "Manage your billing information, make edits to your details, and easily cancel your subscription.", "billing-portal": "Billing Portal", "need-anything-else": "Need anything else?", "billing-assistance-message": "If you require additional assistance regarding billing, our team is readily available to provide support.", "contact-support": "Contact Support", "refer-to-provider-instructions": "Refer to our <guideLink>guides</guideLink> for provider specific instructions.", "sp-download-our-public-cert": "If you want to encrypt the assertion, you can <downloadLink>download our public certificate.</downloadLink> Otherwise select the Unencrypted option.", "welcome-back": "Welcome back", "unlock-account": "Unlock your account", "sso-login": "SSO Login", "signin-with-saml-sso": "Sign in with SAML SSO", "log-in-to-account": "Log in to your account", "desc-sso-login": "Select an Identity Provider to continue with SSO", "desc-signin-with-saml-sso": "Your ID is the slug after the hostname.", "invalid-credentials": "Invalid credentials", "no-credentials": "No credentials provided", "token-not-found": "Verification token not found", "update-webhook": "Update Webhook", "page-not-found": "Page not found", "sorry-not-found": "Sorry, the page you are looking for could not be found.", "error-404": "404", "go-home": "Go Home", "go-back": "Go Back", "error-500": "500", "internal-server-error": "Internal Server Error !", "unable-to-find": "We're unable to find out what's happening! We suggest you to", "try-again-later": "or visit here later.", "multiple-sso-teams": "User belongs to multiple teams with SSO enabled. Please enter your team slug to get started."}