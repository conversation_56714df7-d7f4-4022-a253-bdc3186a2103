NEXTAUTH_URL=http://localhost:4002

# You can use openssl to generate a random 32 character key: openssl rand -base64 32
NEXTAUTH_SECRET=rZTFtfNuSMajLnfFrWT2PZ3lX8WZv7W/Xs2H8hkEY6g=
 
# SMTP / Email settings 
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# If you are using Docker, you can retrieve the values from: docker-compose.yml
DATABASE_URL=postgresql://<USER-HERE>:<PASSWORD-HERE>@localhost:5432/<DATABASE NAME HERE>

APP_URL=http://localhost:4002

SVIX_URL=https://api.eu.svix.com
SVIX_API_KEY=

GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

RETRACED_URL=
RETRACED_API_KEY=
RETRACED_PROJECT_ID=

# Hide landing page and redirect to login page
HIDE_LANDING_PAGE=false

# SSO groups can be prefixed with this identifier in order to avoid conflicts with other groups.
# For example boxyhq-admin would be resolved to admin, boxyhq-member would be resolved to member, etc.
GROUP_PREFIX=boxyhq-

# Users will need to confirm their email before accessing the app feature
CONFIRM_EMAIL=false

# Disable non-business email signup
DISABLE_NON_BUSINESS_EMAIL_SIGNUP=false

# Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=

# If you want to use Jackson that is self-hosted or our SaaS instead of the embedded Jackson that comes with SaaS Starter Kit
# JACKSON_URL=http://localhost:5225
# JACKSON_EXTERNAL_URL=https://sso.eu.boxyhq.com
# JACKSON_API_KEY=secret
# JACKSON_PRODUCT_ID=boxyhq
# JACKSON_WEBHOOK_SECRET=your-webhook-secret

# Enable Auth providers (comma separated)
# Supported providers: github, google, saml, email, credentials, idp-initiated
AUTH_PROVIDERS=

# OpenTelemetry
OTEL_EXPORTER_OTLP_METRICS_ENDPOINT=
OTEL_EXPORTER_OTLP_METRICS_HEADERS=
# OTEL_EXPORTER_OTLP_METRICS_PROTOCOL=grpc
# If you have any issues with using the otel exporter and want to enable debug logs uncomment below
# OTEL_EXPORTER_DEBUG=true 
OTEL_PREFIX=boxyhq.saas

NEXT_PUBLIC_TERMS_URL='/terms'
NEXT_PUBLIC_PRIVACY_URL='/privacy'

NEXT_PUBLIC_DARK_MODE=false

# Team feature
FEATURE_TEAM_SSO=true
FEATURE_TEAM_DSYNC=true
FEATURE_TEAM_AUDIT_LOG=true
FEATURE_TEAM_WEBHOOK=true
FEATURE_TEAM_API_KEY=true
FEATURE_TEAM_DELETION=true
FEATURE_TEAM_PAYMENTS=true

# Google reCAPTCHA
RECAPTCHA_SITE_KEY=
RECAPTCHA_SECRET_KEY=

# Decide which session strategy (jwt or database) to use with NextAuth
NEXTAUTH_SESSION_STRATEGY=jwt

# Sentry
NEXT_PUBLIC_SENTRY_DSN=
NEXT_PUBLIC_SENTRY_TRACE_SAMPLE_RATE=0.0 # https://develop.sentry.dev/sdk/performance/#tracessamplerate
SENTRY_RELEASE=
SENTRY_ENVIRONMENT=

# Sentry CLI (Set these values if you want to upload source maps to Sentry)
SENTRY_URL=
SENTRY_ORG=
SENTRY_PROJECT=
SENTRY_AUTH_TOKEN=

# Max login attempts before account is locked
MAX_LOGIN_ATTEMPTS=5

# Set this to receive Slack notifications, https://hooks.slack.com/services/xxx/xxx/xxx
SLACK_WEBHOOK_URL=

# Stripe
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# Support URL
NEXT_PUBLIC_SUPPORT_URL=
