{"hooks": {}, "git": {"changelog": "git log --pretty=format:\"* %s (%h)\" ${from}...${to}", "requireCleanWorkingDir": true, "requireBranch": "release", "requireUpstream": true, "requireCommits": false, "requireCommitsFail": true, "commitsPath": "", "addUntrackedFiles": false, "commit": true, "commitMessage": "Release ${version}", "commitArgs": [], "tag": true, "tagExclude": null, "tagName": null, "tagMatch": null, "getLatestTagFromAllRefs": false, "tagAnnotation": "Release ${version}", "tagArgs": [], "push": true, "pushArgs": ["--follow-tags"], "pushRepo": ""}, "npm": {"publish": false}, "github": {"release": true, "releaseName": "Release v${version}", "releaseNotes": null, "autoGenerate": false, "preRelease": false, "draft": true, "tokenRef": "GITHUB_TOKEN", "assets": null, "host": null, "timeout": 0, "proxy": null, "skipChecks": false, "web": false, "comments": {"submit": false, "issue": ":rocket: _This issue has been resolved in v${version}. See [${releaseName}](${releaseUrl}) for release notes._", "pr": ":rocket: _This pull request is included in v${version}. See [${releaseName}](${releaseUrl}) for release notes._"}}}