"""This module sets up and provide global custom properties for blender objects,
for example of what type a certain object is. See JSON data under "generalproperties"
for information about the actual properties"""
import bpy, os
from ...services import LogService
from ...steel_lib import *
_LOG = LogService.get_logger('objectproperties.init')
_LOG.trace('initializing object properties module')
from ...services import BlenderConfigSet
_ROOT = os.path.dirname(__file__)
_GENERAL_PROPERTIES_DIR = os.path.join(_ROOT, 'generalproperties')
_GENERAL_PROPERTIES = BlenderConfigSet.get_definitions_in_json_directory(_GENERAL_PROPERTIES_DIR)
GeneralObjectProperties = BlenderConfigSet(_GENERAL_PROPERTIES, bpy.types.Object, prefix='GEN_')
_HUMAN_PROPERTIES_DIR = os.path.join(_ROOT, 'humanproperties')
_HUMAN_PROPERTIES = BlenderConfigSet.get_definitions_in_json_directory(_HUMAN_PROPERTIES_DIR)
HumanObjectProperties = BlenderConfigSet(_HUMAN_PROPERTIES, bpy.types.Object, prefix='HUM_')
_RIG_PROPERTIES_DIR = os.path.join(_ROOT, 'rigproperties')
_RIG_PROPERTIES = BlenderConfigSet.get_definitions_in_json_directory(_RIG_PROPERTIES_DIR)
SkeletonObjectProperties = BlenderConfigSet(_RIG_PROPERTIES, bpy.types.Object, prefix='SKEL_')
__all__ = ['GeneralObjectProperties', 'HumanObjectProperties', 'SkeletonObjectProperties']