# -- Dynamically generated functions for rig.py --

def multiply_with_2(a):
    return a * 2

def less_than_with_110(a):
    return a < 110

def add_with_1(a):
    return a + 1

def greater_than_with_0(a):
    return a > 0

def greater_or_equal_with_3(a):
    return a >= 3

def greater_than_with_1(a):
    return a > 1

def equal_with_1(a):
    return a == 1

def less_than_with_0(a):
    return a < 0

def not_equal_with_0(a):
    return a != 0

def less_than_with_13380(a):
    return a < 13380

def add_with_0(a):
    return a + 0

def less_or_equal_with_0(a):
    return 0 <= a

def not_equal_with_3(a):
    return a != 3

def multiply_with_0_dot_15(a):
    return 0.15 * a

def divide_with_2(a):
    return a / 2
