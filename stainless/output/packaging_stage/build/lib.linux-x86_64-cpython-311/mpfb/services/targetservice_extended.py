# -- Dynamically generated functions for targetservice.py --

def less_than_with_0_dot_0(a):
    return a < 0.0

def less_than_with_0_dot_0001(a):
    return a < 0.0001

def multiply_with_3(a):
    return a * 3

def add_with_1(a):
    return a + 1

def add_with_2(a):
    return a + 2

def less_than_with_0(a):
    return a < 0

def less_than_with_1(a):
    return a < 1

def greater_than_with_0(a):
    return a > 0

def subtract_with_1(a):
    return 1 - a

def greater_than_with_0_dot_0001(a):
    return a > 0.0001

def greater_than_with_60(a):
    return a > 60
