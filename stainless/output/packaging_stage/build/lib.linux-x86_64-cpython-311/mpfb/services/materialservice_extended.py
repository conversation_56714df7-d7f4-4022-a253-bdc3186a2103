# -- Dynamically generated functions for materialservice.py --

def greater_than_with_0(a):
    return a > 0

def equal_with_0(a):
    return a == 0

def less_than_with_1(a):
    return a < 1

def multiply_with_300(a):
    return 300 * a

def add_with_1(a):
    return a + 1

def add_with_1100(a):
    return a + 1100

def add_with_1000(a):
    return a + 1000

def greater_than_with_1(a):
    return a > 1

def subtract_with_1(a):
    return a - 1
