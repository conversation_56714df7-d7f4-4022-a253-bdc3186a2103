# Surreal Steel: The Over-Engineered Python-to-Cython Transformer

Welcome to Surreal Steel, a command-line tool designed with a singular, obsessive purpose: to take your standard Python Blender addon and forge it into a high-performance, compiled Cython extension. It's a digital blacksmith, hammering away at your code until it's faster, stronger, and ready for distribution.

## The Mannerisms of the Machine

Surreal Steel is not a quiet tool. It's an over-caffeinated, meticulous, and slightly verbose robot that believes in transparency. It will tell you exactly what it's doing at every step of the process, using a colorful array of logs to narrate its journey.

*   **V<PERSON><PERSON>e by <PERSON><PERSON>ult:** It loves to talk. You'll see it announce when it finds files, when it's compiling, when it's packaging, and especially when it encounters an error. It believes there's no such thing as too much information. If you prefer a quieter workshop, you can use the `--compact` flag to tell it to be less chatty.
*   **Obsessed with Performance:** The entire reason for its existence is to squeeze every last drop of performance out of your Python code. It does this by targeting one of Python's biggest performance bottlenecks: dynamic numeric operations. By converting operations like `x + 1.0` into dedicated, compiled C functions, it aims to reduce Python interpreter overhead.
*   **Safety-Conscious (Mostly):** It understands that your source code is sacred. It will never modify your original files. All its work is done in a separate output directory. However, it has a destructive side that you can enable with flags like `--clean` and `--remove-originals`. It will warn you before it deletes things, but it will do what you tell it to. Handle with care.
*   **Batch-Oriented:** It's built to work at scale. Point it at a folder full of addons, and it will happily churn through them one by one, giving each the full "Surreal Steel" treatment.

---

## The Forging Process: What It Actually Does

The transformation from plain Python to hardened steel follows a precise, multi-stage pipeline.

### 1. Analysis and Code Transformation
First, the tool recursively scans your input directory for all `.py` files. For each file, it performs the following:
*   **AST Parsing:** It reads your Python code not as text, but as an Abstract Syntax Tree (AST). This gives it a deep, structural understanding of your code's logic.
*   **Literal Detection:** It traverses the AST, hunting for `BinOp` (e.g., `+`, `-`, `*`, `/`), `Compare` (e.g., `==`, `>`, `<`), and `AugAssign` (e.g., `+=`, `-=`) nodes that involve a numeric literal (like `5`, `1.23`).
*   **Specialized Function Generation:** For every unique operation and literal pair it finds (e.g., adding `1`, comparing against `0.0`), it dynamically generates a new, highly specialized Python function. For example, `x + 1` might become a call to `add_1(x)`. These new functions are collected into dedicated helper modules (e.g., `math_extended_your_module.py` or `_steel_init_helpers.py`).
*   **AST Rewriting:** It rewrites the original AST, replacing the standard Python operations with calls to its newly generated specialized functions.
*   **Code Generation:** Finally, it converts the modified AST back into Python code. This transformed code, now peppered with calls to the performance-tuned helper functions, is saved in the output directory.

### 2. Cython Compilation
This is where the magic happens. The tool takes all the transformed Python files and the generated helper modules and feeds them to Cython. Cython converts this Python code into optimized C code and then compiles that C code into native, shared library modules (`.so` files on Linux). These are blazing-fast binary extensions that the Python interpreter can load directly.

### 3. Packaging
Once the code is compiled, Surreal Steel can package it for distribution.
*   **Wheel Packaging (`--package`):** It can create a standard Python Wheel (`.whl`) file. It automatically generates the necessary `setup.py` and `MANIFEST.in` files, ensuring the compiled `.so` files are included. This is the modern, preferred format for Blender addons.
*   **Zip Packaging (`--zip`):** It can also create a simple `.zip` archive, which is suitable for legacy addons or for distribution channels that don't support wheels.

### 4. Verification (`--blender-check`)
To ensure the final product actually works, the tool can launch a headless instance of Blender in a clean, factory-default state. It will attempt to install and enable the newly packaged addon, reporting back with a success or failure. This provides a final, crucial sanity check.

---

## Command-Line Arguments

Here is a detailed breakdown of every command and flag you can use to control the forging process.

### Main Command
`node steel.js <input> [options]`
*   **`input`**: (Required) The path to the addon project directory you want to process.

### Standalone Check Command
`node steel.js check <path>`
*   **`path`**: A path to an *existing* addon file (`.zip`, `.whl`) or a directory containing addons. This command runs *only* the Blender verification step without any transformation or packaging. It's useful for testing pre-built addons.

### Options
*   **`-o, --output <path>`**
    Specifies the directory where all output files will be stored. If not provided, it defaults to a new directory in the current working directory named `<input_directory_name>_steel`.

*   **`--compact`**
    Enables a quieter, more compact logging output. Hides non-essential `info` and `debug` messages, showing only major steps, warnings, and errors.

*   **`--batch-folders`**
    Treats every subfolder within the `<input>` directory as a separate, independent addon. It will run the entire processing pipeline on each subfolder.

*   **`--clean`**
    A destructive flag. Before the run begins, it will completely **delete** the specified output directory if it exists. In batch mode, this cleans the main output directory only once at the very start.

*   **`--package-wheel`**
    **[RECOMMENDED]** Performs complete wheel packaging with all best practices: creates a Python Wheel (`.whl`), moves it to a `wheels/` subdirectory, **automatically removes ALL Python source files from the output directory** (keeping only compiled extensions and other assets), cleans Python source files from within the wheel itself, and updates the `blender_manifest.toml` to reference the wheel. This produces the cleanest possible wheel packages without requiring the separate `--clean` flag. This requires a `blender_manifest.toml` file to be present in the addon's root. If the manifest is not found (i.e., it's a legacy addon), this option will be automatically disabled with a warning.

*   **`--package`** ⚠️ **[DEPRECATED]**
    Enables packaging the addon as a Python Wheel (`.whl`). **Use `--package-wheel` instead for complete wheel packaging.**

*   **`--update-manifest`** ⚠️ **[DEPRECATED]**
    When used with `--package`, this flag will modify the `blender_manifest.toml` in the *output directory* to include a reference to the newly created wheel file in its `wheels` array. **Use `--package-wheel` instead for complete wheel packaging.**

*   **`--use-wheels-dir`** ⚠️ **[DEPRECATED]**
    When used with `--package`, this moves the final `.whl` file into a `wheels/` subdirectory inside the addon's output folder, as is common practice for Blender addons. The path in the manifest (if `--update-manifest` is also used) will reflect this new location. **Use `--package-wheel` instead for complete wheel packaging.**

*   **`--remove-originals, --rm`**
    A highly destructive flag. After the Cython compilation is successful, this will delete the transformed `.py` source files from the output directory, leaving only the compiled `.so` extensions. Use with extreme caution.

*   **`--zip`**
    Creates a final distributable `.zip` archive of the processed addon. The zip file will be placed in the root of the output directory.

*   **`--clean-after-zip`**
    When used with `--zip`, this will delete the intermediate build subfolder (e.g., `output/my_addon/`) after the zip file has been successfully created, leaving only the final `.zip` archive in the output directory.

*   **`--blender-check`**
    After all processing and packaging is complete, this will run the final verification step using a headless Blender instance.

---

## Migration Guide: Simplified Wheel Packaging

**TL;DR:** Replace `--package --use-wheels-dir --update-manifest` with `--package-wheel`

### Before (Deprecated)
```bash
node steel.js my_addon --package --use-wheels-dir --update-manifest
```

### After (Recommended)
```bash
node steel.js my_addon --package-wheel
```

### What `--package-wheel` Does
The new `--package-wheel` flag combines the functionality of three separate flags plus automatic cleanup:
1. **`--package`**: Creates a Python wheel (.whl) file
2. **`--use-wheels-dir`**: Moves the wheel to a `wheels/` subdirectory and automatically cleans Python source files from the wheel
3. **`--update-manifest`**: Updates `blender_manifest.toml` to include the wheel path in the `wheels` array
4. **Automatic cleanup**: Removes ALL Python source files from the output directory before wheel creation (equivalent to selective `--clean` behavior)

### Benefits
- **Simpler command line**: One flag instead of three (or four with `--clean`)
- **Best practices by default**: Automatically follows Blender addon packaging conventions
- **Cleanest possible wheels**: Python source files are automatically removed from both the output directory AND the wheel itself, keeping only compiled extensions and necessary assets
- **No manual cleanup needed**: No need to use `--clean` flag separately
- **No configuration needed**: Works out of the box with sensible defaults

### Backward Compatibility
The old flags still work but will show deprecation warnings. They will be removed in a future version.
